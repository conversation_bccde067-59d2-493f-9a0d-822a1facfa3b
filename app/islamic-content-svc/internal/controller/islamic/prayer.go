package islamic

import (
	"context"
	"fmt"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"halalplus/utility/token"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type ControllerPrayer struct {
	v1.UnimplementedPrayerServiceServer
}

func (*ControllerPrayer) GetCalendar(ctx context.Context, req *v1.CalendarReq) (res *v1.CalendarRes, err error) {
	// 参数验证
	if req.Year <= 0 || req.Month <= 0 || req.Month > 12 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "invalid year or month")
	}

	// 转换protobuf请求为内部参数
	methodCode := req.MethodCode
	if methodCode == "" || methodCode == "AUTO" {
		methodCode = "UMMUL_QURA"
	}

	input := &model.CalendarGetInput{
		Year:           int32(req.Year),
		Month:          int32(req.Month),
		MethodCode:     methodCode,
		DateAdjustment: req.DateAdjustment,
	}

	// 调用service层获取日历数据
	calendarData, err := service.Prayer().GetCalendar(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 使用gconv.Structs转换数据结构
	calendarList := make([]*v1.CalendarDateInfo, 0, len(calendarData))
	err = gconv.Structs(calendarData, &calendarList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.CalendarRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.CalendarData{
			List: calendarList,
		},
	}, nil
}

// GetBatchCalendar 批量获取多个年月的日历数据
func (*ControllerPrayer) GetBatchCalendar(ctx context.Context, req *v1.BatchCalendarReq) (res *v1.BatchCalendarRes, err error) {
	// 参数验证
	if len(req.YearMonths) == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "year_months cannot be empty")
	}

	// 限制批量查询的数量，避免过多请求
	if len(req.YearMonths) > 12 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "too many year_months, maximum 12 allowed")
	}

	// 转换protobuf请求为内部参数
	methodCode := req.MethodCode
	if methodCode == "" || methodCode == "AUTO" {
		methodCode = "UMMUL_QURA"
	}

	input := &model.BatchCalendarGetInput{
		YearMonths:     req.YearMonths,
		MethodCode:     methodCode,
		DateAdjustment: req.DateAdjustment,
	}

	// 调用service层获取批量日历数据
	batchCalendarData, err := service.Prayer().GetBatchCalendar(ctx, input)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换
	calendarsMap := make(map[string]*v1.CalendarData)
	for yearMonth, calendarDateInfoList := range batchCalendarData {
		// 使用gconv.Structs转换数据结构
		calendarList := make([]*v1.CalendarDateInfo, 0, len(calendarDateInfoList))
		err = gconv.Structs(calendarDateInfoList, &calendarList)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("failed to convert calendar data for %s: %v", yearMonth, err))
			continue
		}

		calendarsMap[yearMonth] = &v1.CalendarData{
			List: calendarList,
		}
	}

	return &v1.BatchCalendarRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.BatchCalendarData{
			Calendars: calendarsMap,
		},
	}, nil
}

// GetDailyPrayerTimes 获取每日祷告时间
func (*ControllerPrayer) GetDailyPrayerTime(ctx context.Context, req *v1.GetDailyPrayerTimeReq) (res *v1.GetDailyPrayerTimeRes, err error) {
	dateTime, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		g.Log().Error(ctx, "invalid date format:", err)
		return nil, err
	}

	input := &model.DailyPrayerTimeInput{
		Year:           dateTime.Year(),
		Month:          int(dateTime.Month()),
		Day:            dateTime.Day(),
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: int(req.DateAdjustment),
	}

	output, err := service.Prayer().GetDailyPrayerTime(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var prayerTimesData *v1.PrayerTimeData
	if err = gconv.Struct(output, &prayerTimesData); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetDailyPrayerTimeRes{
		Code: 200,
		Msg:  "success",
		Data: prayerTimesData,
	}, nil
}

// GetMonthlyPrayerTimes 获取月度祷告时间
func (*ControllerPrayer) GetMonthlyPrayerTimes(ctx context.Context, req *v1.GetMonthlyPrayerTimesReq) (res *v1.GetMonthlyPrayerTimesRes, err error) {

	input := &model.MonthlyPrayerTimesInput{
		Year:           int(req.Year),
		Month:          int(req.Month),
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Timezone:       req.Timezone,
		MethodCode:     req.MethodCode,
		DateAdjustment: int(req.DateAdjustment),
	}

	output, err := service.Prayer().GetMonthlyPrayerTimes(ctx, input)
	if err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	var timeList []*v1.PrayerTimeData
	if err = gconv.Structs(output, &timeList); err != nil {
		g.Log().Error(ctx, "GetPrayerTimes error:", err)
		return nil, err
	}

	return &v1.GetMonthlyPrayerTimesRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.MonthlyPrayerTimesData{
			List: timeList,
		},
	}, nil
}

// GetHajiJadwalList 获取朝觐日程列表
func (*ControllerPrayer) GetHajiJadwalList(ctx context.Context, req *v1.HajiJadwalListReq) (res *v1.HajiJadwalListRes, err error) {
	// 获取语言ID
	languageId := token.GetLanguageId(ctx)

	// 调用service层获取朝觐日程列表
	output, err := service.Prayer().GetHajiJadwalList(ctx, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换数据结构
	var jadwalList []*v1.HajiJadwalInfo
	err = gconv.Structs(output.List, &jadwalList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiJadwalListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiJadwalListData{
			List:        jadwalList,
			Description: output.Description,
			Year:        output.Year,
		},
	}, nil
}

// GetHajiJadwalDetail 获取朝觐日程详情
func (*ControllerPrayer) GetHajiJadwalDetail(ctx context.Context, req *v1.HajiJadwalDetailReq) (res *v1.HajiJadwalDetailRes, err error) {
	// 参数验证
	if req.JadwalId == 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "jadwal_id cannot be empty")
	}

	// 获取语言ID
	languageId := token.GetLanguageId(ctx)

	// 调用service层获取朝觐日程详情
	output, err := service.Prayer().GetHajiJadwalDetail(ctx, req.JadwalId, languageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换数据结构
	var jadwal v1.HajiJadwalInfo
	err = gconv.Struct(output.Jadwal, &jadwal)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	return &v1.HajiJadwalDetailRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.HajiJadwalDetailData{
			Jadwal: &jadwal,
		},
	}, nil
}
