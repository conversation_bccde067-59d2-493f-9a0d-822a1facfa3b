package islamic

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/service"
	"sort"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
)

type Controller struct {
	v1.UnimplementedSurahServiceServer
}

type ControllerNews struct {
	v1.UnimplementedNewsServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterSurahServiceServer(s.Server, &Controller{})
	v1.RegisterNewsServiceServer(s.Server, &ControllerNews{})
	v1.RegisterBannerServiceServer(s.Server, &ControllerBanner{})
	v1.RegisterFaqServiceServer(s.Server, &ControllerFaq{})
	v1.RegisterPrayerServiceServer(s.Server, &ControllerPrayer{})
	v1.RegisterWisdomServiceServer(s.Server, &ControllerWisdom{})
	v1.RegisterVideoServiceServer(s.Server, &ControllerVideo{})
}

func (*Controller) TahlilList(ctx context.Context, req *v1.TahlilListReq) (res *v1.TahlilListRes, err error) {
	res = service.Islamic().TahlilList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*Controller) SurahList(ctx context.Context, req *v1.SurahListReq) (res *v1.SurahListRes, err error) {
	res = service.Islamic().SurahList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*Controller) JuzList(ctx context.Context, req *v1.JuzListReq) (res *v1.JuzListRes, err error) {
	res = &v1.JuzListRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.JuzParamInput{
		Name: req.Name,
	}
	juzList := service.Islamic().JuzList(ctx, JuzParamInput)

	outList := make([]*v1.JuzInfo, 0, 30)
	for _, juz := range juzList {
		outList = append(outList, &v1.JuzInfo{
			StartSurahId:   uint32(juz.StartSurahId),
			StartSurahName: juz.StartSurahName,
			EndSurahId:     uint32(juz.EndSurahId),
			EndSurahName:   juz.EndSurahName,
			StartAyahId:    uint32(juz.StartAyahId),
			EndAyahId:      uint32(juz.EndAyahId),
			Juz:            juz.Name,
			FirstWord:      juz.FirstWord,
		})
	}
	//Data 排序
	sort.Slice(outList, func(i, j int) bool {
		return outList[i].StartSurahId < outList[j].StartSurahId
	})
	juzListResData := &v1.JuzListResData{
		List: outList,
	}
	res.Data = juzListResData
	return res, nil

}

func (*Controller) AyahList(ctx context.Context, req *v1.AyahListReq) (res *v1.AyahListRes, err error) {
	res = service.Islamic().AyahList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

// 阅读记录
func (*Controller) AyahReadRecord(ctx context.Context, req *v1.AyahReadRecordReq) (res *v1.AyahReadRecordRes, err error) {

	res = &v1.AyahReadRecordRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadRecordInput{
		AyahId:   req.AyahId,
		IsUserOp: req.IsUserOp,
	}
	service.Islamic().AyahReadRecord(ctx, JuzParamInput)
	return res, nil

}
func (*Controller) AyahReadRecordList(ctx context.Context, req *v1.AyahReadRecordListReq) (res *v1.AyahReadRecordListRes, err error) {
	res = service.Islamic().AyahReadRecordList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil

}

// 阅读收藏
func (*Controller) AyahReadCollect(ctx context.Context, req *v1.AyahReadCollectReq) (res *v1.AyahReadCollectRes, err error) {

	res = &v1.AyahReadCollectRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.AyahReadCollectInput{
		AyahId: req.AyahId,
	}
	service.Islamic().AyahReadCollect(ctx, JuzParamInput)
	return res, nil
}
func (*Controller) CheckAyahReadCollectStatus(ctx context.Context, req *v1.CheckAyahReadCollectStatusReq) (res *v1.CheckAyahReadCollectStatusRes, err error) {

	res = &v1.CheckAyahReadCollectStatusRes{}
	res.Code = 200
	res.Msg = "success"
	res.Data = &v1.CheckAyahReadCollectStatusResData{
		IsCollect: 1, // 默认未收藏
	}
	JuzParamInput := &model.CheckAyahReadCollectStatusInput{
		AyahId: req.AyahId,
	}
	CollectStatus := service.Islamic().CheckAyahReadCollectStatus(ctx, JuzParamInput)
	if CollectStatus != nil {
		if CollectStatus.IsCollect == 0 {
			res.Data.IsCollect = 1
		} else {
			res.Data.IsCollect = 2
		}
	}
	return res, nil

}

// 阅读记录列表
func (*Controller) AyahReadCollectList(ctx context.Context, req *v1.AyahReadCollectListReq) (res *v1.AyahReadCollectListRes, err error) {
	res = service.Islamic().AyahReadCollectList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil

}

//func (*Controller) SurahInfo(ctx context.Context, req *v1.SurahInfoReq) (res *v1.SurahInfoRes, err error) {
//
//	res = &v1.SurahInfoRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratDaftar{
//		Id:          1,
//		Nama:        "212323",
//		Nomor:       1,
//		NamaLatin:   "Pembukaan",
//		JumlahAyat:  4,
//		TempatTurun: "Pembukaan",
//		Arti:        "Pembukaan",
//		Deskripsi:   "Pembukaan",
//		Audio:       "https://equran.nos.wjv-1.neo.id/audio-full/Misyari-Rasyid-Al-Afasi/012.mp3",
//		Status:      1,
//	}
//	return res, nil
//}
//
//func (*Controller) SurahDesc(ctx context.Context, req *v1.SurahDescReq) (res *v1.SurahDescRes, err error) {
//
//	res = &v1.SurahDescRes{}
//	res.Code = 200
//	res.Msg = "success"
//	res.Data = &pbentity.SuratTafsir{
//		Id:        1,
//		TafsirId:  1,
//		SurahId:   1,
//		AyatNomor: 1,
//		Tafsir:    "Pembukaan",
//	}
//	return res, nil
//}

func (*ControllerNews) NewsCategoryList(ctx context.Context, req *v1.NewsCategoryListReq) (res *v1.NewsCategoryListRes, err error) {

	res = &v1.NewsCategoryListRes{}
	res.Code = 200
	res.Msg = "success"
	ParamInput := &model.NewsCategoryListInput{
		Pid: req.Pid,
	}
	CategoryList := service.Islamic().NewsCategoryList(ctx, ParamInput)

	outList := make([]*v1.CategoryInfo, 0, len(CategoryList)) // 初始化切片，预分配空间
	for _, cate := range CategoryList {
		outList = append(outList, &v1.CategoryInfo{
			Id:         cate.Id,
			ParentId:   cate.ParentId,
			LanguageId: cate.LanguageId,
			Name:       cate.Name,
			CoverImgs:  cate.CoverImgs,
		})
	}

	newsCategoryListResData := &v1.NewsCategoryListResData{
		List: outList,
	}
	res.Data = newsCategoryListResData
	return res, nil
}

func (*ControllerNews) NewsListByCateId(ctx context.Context, req *v1.NewsListByCateIdReq) (res *v1.NewsListByCateIdRes, err error) {
	res = service.Islamic().NewsListByCateId(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*ControllerNews) NewsTopicList(ctx context.Context, req *v1.NewsTopicListReq) (res *v1.NewsTopicListRes, err error) {

	res = &v1.NewsTopicListRes{}
	res.Code = 200
	res.Msg = "success"
	ParamInput := &model.NewsTopicListInput{}
	TopicList := service.Islamic().NewsTopicList(ctx, ParamInput)

	outList := make([]*v1.TopicInfo, 0, len(TopicList)) // 初始化切片，预分配空间
	for _, cate := range TopicList {
		outList = append(outList, &v1.TopicInfo{
			TopicId:    cate.Id,
			LanguageId: cate.LanguageId,
			Name:       cate.Name,
			ShortName:  cate.ShortName,
			TopicImgs:  cate.TopicImgs,
		})
	}
	newsTopicListResData := &v1.NewsTopicListResData{
		List: outList,
	}
	res.Data = newsTopicListResData

	return res, nil

}

func (*ControllerNews) NewsListByTopicId(ctx context.Context, req *v1.NewsListByTopicIdReq) (res *v1.NewsListByTopicIdRes, err error) {
	res = service.Islamic().NewsListByTopicId(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*ControllerNews) NewsInfo(ctx context.Context, req *v1.NewsInfoReq) (res *v1.NewsInfoRes, err error) {

	res = &v1.NewsInfoRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsInfoInput{}
	gconv.Struct(req, JuzParamInput)
	rtList := service.Islamic().NewsInfo(ctx, JuzParamInput)
	Data := &v1.ArticleInfo{}
	if len(rtList) == 0 {
		return res, nil
	}
	gconv.Struct(rtList[0], Data)
	res.Data = Data
	return res, nil
}

func (*ControllerNews) NewsHotList(ctx context.Context, req *v1.NewsHotListReq) (res *v1.NewsHotListRes, err error) {

	res = service.Islamic().NewsHotList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*ControllerNews) NewsCollectList(ctx context.Context, req *v1.NewsCollectReq) (res *v1.NewsCollectRes, err error) {

	res = service.Islamic().NewsCollectList(ctx, req)
	res.Code = 200
	res.Msg = "success"
	return res, nil
}

func (*ControllerNews) NewsCollectStatusCheck(ctx context.Context, req *v1.NewsCollectStatusCheckReq) (res *v1.NewsCollectStatusCheckRes, err error) {
	res = &v1.NewsCollectStatusCheckRes{}
	res.Code = 200
	res.Msg = "success"
	res.Data = &v1.NewsCollectStatusCheckData{
		IsCollect: 1, // 默认未收藏
	}
	JuzParamInput := &model.NewsCollectStatusCheckInput{
		ArticleId: uint32(req.ArticleId),
	}
	CollectStatus := service.Islamic().NewsCollectStatusCheck(ctx, JuzParamInput)
	if CollectStatus != nil {
		if CollectStatus.IsCollect == 0 {
			res.Data.IsCollect = 1 // 未收藏
		} else {
			res.Data.IsCollect = 2 // 已收藏
		}
	}
	return res, nil
}

func (*ControllerNews) NewsCollectOp(ctx context.Context, req *v1.NewsCollectOpReq) (res *v1.NewsCollectOpRes, err error) {
	res = &v1.NewsCollectOpRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectOpInput{
		ArticleId: uint32(req.ArticleId),
	}
	service.Islamic().NewsCollectOp(ctx, JuzParamInput)
	return res, nil
}

func (*ControllerNews) NewsViewOp(ctx context.Context, req *v1.NewsViewOpReq) (res *v1.NewsViewOpRes, err error) {
	res = &v1.NewsViewOpRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectOpInput{
		ArticleId: uint32(req.ArticleId),
	}
	service.Islamic().NewsViewOp(ctx, JuzParamInput)
	return res, nil
}

func (*ControllerNews) NewsShareOp(ctx context.Context, req *v1.NewsShareOpReq) (res *v1.NewsShareOpRes, err error) {
	res = &v1.NewsShareOpRes{}
	res.Code = 200
	res.Msg = "success"
	JuzParamInput := &model.NewsCollectOpInput{
		ArticleId: uint32(req.ArticleId),
	}
	service.Islamic().NewsShareOp(ctx, JuzParamInput)
	return res, nil
}

func (*Controller) GetHajiJadwalList(ctx context.Context, req *v1.HajiJadwalListReq) (res *v1.HajiJadwalListRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) GetHajiJadwalDetail(ctx context.Context, req *v1.HajiJadwalDetailReq) (res *v1.HajiJadwalDetailRes, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
