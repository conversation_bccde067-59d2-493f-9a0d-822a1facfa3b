// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/surat_tafsir.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SuratTafsir struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                  //
	TafsirId      int32                  `protobuf:"varint,2,opt,name=TafsirId,proto3" json:"TafsirId,omitempty" dc:"注释全局ID"`          // 注释全局ID
	SurahId       int32                  `protobuf:"varint,3,opt,name=SurahId,proto3" json:"SurahId,omitempty" dc:"所属章节ID"`            // 所属章节ID
	AyatNomor     int32                  `protobuf:"varint,4,opt,name=AyatNomor,proto3" json:"AyatNomor,omitempty" dc:"对应经文编号"`        // 对应经文编号
	Tafsir        string                 `protobuf:"bytes,5,opt,name=Tafsir,proto3" json:"Tafsir,omitempty" dc:"注释内容"`                 // 注释内容
	CreatedTime   uint64                 `protobuf:"varint,6,opt,name=CreatedTime,proto3" json:"CreatedTime,omitempty" dc:"创建时间戳(毫秒)"` // 创建时间戳(毫秒)
	UpdatedTime   uint64                 `protobuf:"varint,7,opt,name=UpdatedTime,proto3" json:"UpdatedTime,omitempty" dc:"修改时间戳(毫秒)"` // 修改时间戳(毫秒)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuratTafsir) Reset() {
	*x = SuratTafsir{}
	mi := &file_pbentity_surat_tafsir_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuratTafsir) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratTafsir) ProtoMessage() {}

func (x *SuratTafsir) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surat_tafsir_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratTafsir.ProtoReflect.Descriptor instead.
func (*SuratTafsir) Descriptor() ([]byte, []int) {
	return file_pbentity_surat_tafsir_proto_rawDescGZIP(), []int{0}
}

func (x *SuratTafsir) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratTafsir) GetTafsirId() int32 {
	if x != nil {
		return x.TafsirId
	}
	return 0
}

func (x *SuratTafsir) GetSurahId() int32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *SuratTafsir) GetAyatNomor() int32 {
	if x != nil {
		return x.AyatNomor
	}
	return 0
}

func (x *SuratTafsir) GetTafsir() string {
	if x != nil {
		return x.Tafsir
	}
	return ""
}

func (x *SuratTafsir) GetCreatedTime() uint64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *SuratTafsir) GetUpdatedTime() uint64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

var File_pbentity_surat_tafsir_proto protoreflect.FileDescriptor

const file_pbentity_surat_tafsir_proto_rawDesc = "" +
	"\n" +
	"\x1bpbentity/surat_tafsir.proto\x12\bpbentity\"\xcd\x01\n" +
	"\vSuratTafsir\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x1a\n" +
	"\bTafsirId\x18\x02 \x01(\x05R\bTafsirId\x12\x18\n" +
	"\aSurahId\x18\x03 \x01(\x05R\aSurahId\x12\x1c\n" +
	"\tAyatNomor\x18\x04 \x01(\x05R\tAyatNomor\x12\x16\n" +
	"\x06Tafsir\x18\x05 \x01(\tR\x06Tafsir\x12 \n" +
	"\vCreatedTime\x18\x06 \x01(\x04R\vCreatedTime\x12 \n" +
	"\vUpdatedTime\x18\a \x01(\x04R\vUpdatedTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_surat_tafsir_proto_rawDescOnce sync.Once
	file_pbentity_surat_tafsir_proto_rawDescData []byte
)

func file_pbentity_surat_tafsir_proto_rawDescGZIP() []byte {
	file_pbentity_surat_tafsir_proto_rawDescOnce.Do(func() {
		file_pbentity_surat_tafsir_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_surat_tafsir_proto_rawDesc), len(file_pbentity_surat_tafsir_proto_rawDesc)))
	})
	return file_pbentity_surat_tafsir_proto_rawDescData
}

var file_pbentity_surat_tafsir_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surat_tafsir_proto_goTypes = []any{
	(*SuratTafsir)(nil), // 0: pbentity.SuratTafsir
}
var file_pbentity_surat_tafsir_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_surat_tafsir_proto_init() }
func file_pbentity_surat_tafsir_proto_init() {
	if File_pbentity_surat_tafsir_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_surat_tafsir_proto_rawDesc), len(file_pbentity_surat_tafsir_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surat_tafsir_proto_goTypes,
		DependencyIndexes: file_pbentity_surat_tafsir_proto_depIdxs,
		MessageInfos:      file_pbentity_surat_tafsir_proto_msgTypes,
	}.Build()
	File_pbentity_surat_tafsir_proto = out.File
	file_pbentity_surat_tafsir_proto_goTypes = nil
	file_pbentity_surat_tafsir_proto_depIdxs = nil
}
